import { Cloudinary } from '@cloudinary/url-gen';
import { 
  fill, 
  scale, 
  crop, 
  thumb,
  fit,
  pad
} from '@cloudinary/url-gen/actions/resize';
import { 
  quality, 
  format,
  dpr
} from '@cloudinary/url-gen/actions/delivery';
import { 
  focusOn,
  gravity
} from '@cloudinary/url-gen/qualifiers/gravity';
import { face, center, autoGravity } from '@cloudinary/url-gen/qualifiers/focusOn';
import { auto } from '@cloudinary/url-gen/qualifiers/quality';
import { auto as autoFormat } from '@cloudinary/url-gen/qualifiers/format';
import { cloudinaryConfig, TRANSFORMATIONS } from './config';

// Initialize Cloudinary instance for transformations
const cld = new Cloudinary({
  cloud: {
    cloudName: cloudinaryConfig.cloudName
  },
  url: {
    secure: cloudinaryConfig.secure
  }
});

// Transformation options interface
export interface TransformationOptions {
  width?: number;
  height?: number;
  crop?: 'fill' | 'scale' | 'fit' | 'pad' | 'thumb';
  gravity?: 'face' | 'center' | 'auto';
  quality?: 'auto' | number;
  format?: 'auto' | 'webp' | 'jpg' | 'png';
  dpr?: 'auto' | number;
}

// Generate optimized image URL
export const getOptimizedImageUrl = (
  publicId: string, 
  options: TransformationOptions = {}
): string => {
  if (!publicId || !cloudinaryConfig.cloudName) {
    return '';
  }

  try {
    const image = cld.image(publicId);

    // Apply transformations
    if (options.width || options.height) {
      const resizeOptions: any = {};
      if (options.width) resizeOptions.width = options.width;
      if (options.height) resizeOptions.height = options.height;

      switch (options.crop) {
        case 'fill':
          image.resize(fill().width(resizeOptions.width).height(resizeOptions.height));
          break;
        case 'scale':
          image.resize(scale().width(resizeOptions.width).height(resizeOptions.height));
          break;
        case 'fit':
          image.resize(fit().width(resizeOptions.width).height(resizeOptions.height));
          break;
        case 'pad':
          image.resize(pad().width(resizeOptions.width).height(resizeOptions.height));
          break;
        case 'thumb':
          image.resize(thumb().width(resizeOptions.width).height(resizeOptions.height));
          break;
        default:
          image.resize(fill().width(resizeOptions.width).height(resizeOptions.height));
      }
    }

    // Apply gravity
    if (options.gravity) {
      switch (options.gravity) {
        case 'face':
          image.resize(fill().gravity(focusOn(face())));
          break;
        case 'center':
          image.resize(fill().gravity(gravity(center())));
          break;
        case 'auto':
          image.resize(fill().gravity(autoGravity()));
          break;
      }
    }

    // Apply quality
    if (options.quality === 'auto') {
      image.delivery(quality(auto()));
    } else if (typeof options.quality === 'number') {
      image.delivery(quality(options.quality));
    } else {
      image.delivery(quality(auto()));
    }

    // Apply format
    if (options.format === 'auto') {
      image.delivery(format(autoFormat()));
    } else if (options.format) {
      image.delivery(format(options.format));
    } else {
      image.delivery(format(autoFormat()));
    }

    // Apply DPR for responsive images
    if (options.dpr === 'auto') {
      image.delivery(dpr('auto'));
    } else if (typeof options.dpr === 'number') {
      image.delivery(dpr(options.dpr));
    }

    return image.toURL();
  } catch (error) {
    console.error('Error generating Cloudinary URL:', error);
    return '';
  }
};

// Predefined transformation functions
export const getRestaurantCardImage = (publicId: string): string => {
  return getOptimizedImageUrl(publicId, TRANSFORMATIONS.RESTAURANT_CARD);
};

export const getRestaurantHeroImage = (publicId: string): string => {
  return getOptimizedImageUrl(publicId, TRANSFORMATIONS.RESTAURANT_HERO);
};

export const getMenuItemImage = (publicId: string): string => {
  return getOptimizedImageUrl(publicId, TRANSFORMATIONS.MENU_ITEM);
};

export const getMenuItemThumb = (publicId: string): string => {
  return getOptimizedImageUrl(publicId, TRANSFORMATIONS.MENU_ITEM_THUMB);
};

export const getUserAvatar = (publicId: string): string => {
  return getOptimizedImageUrl(publicId, TRANSFORMATIONS.AVATAR);
};

export const getUserAvatarSmall = (publicId: string): string => {
  return getOptimizedImageUrl(publicId, TRANSFORMATIONS.AVATAR_SMALL);
};

// Responsive image URLs for different screen sizes
export const getResponsiveImageUrls = (publicId: string) => {
  return {
    mobile: getOptimizedImageUrl(publicId, { width: 400, height: 300, crop: 'fill' }),
    tablet: getOptimizedImageUrl(publicId, { width: 600, height: 450, crop: 'fill' }),
    desktop: getOptimizedImageUrl(publicId, { width: 800, height: 600, crop: 'fill' }),
    large: getOptimizedImageUrl(publicId, { width: 1200, height: 900, crop: 'fill' }),
  };
};

// Generate srcSet for responsive images
export const generateSrcSet = (publicId: string, baseWidth: number = 400): string => {
  const widths = [baseWidth, baseWidth * 1.5, baseWidth * 2, baseWidth * 3];
  
  return widths
    .map(width => {
      const url = getOptimizedImageUrl(publicId, { 
        width: Math.round(width), 
        crop: 'fill',
        quality: 'auto',
        format: 'auto',
        dpr: 'auto'
      });
      return `${url} ${width}w`;
    })
    .join(', ');
};

// Extract public ID from Cloudinary URL
export const extractPublicId = (cloudinaryUrl: string): string => {
  if (!cloudinaryUrl) return '';
  
  try {
    // Match pattern: https://res.cloudinary.com/cloud_name/image/upload/v123456/folder/public_id.ext
    const match = cloudinaryUrl.match(/\/upload\/(?:v\d+\/)?(.+?)(?:\.[^.]+)?$/);
    return match ? match[1] : '';
  } catch (error) {
    console.error('Error extracting public ID:', error);
    return '';
  }
};

// Check if URL is a Cloudinary URL
export const isCloudinaryUrl = (url: string): boolean => {
  return url.includes('res.cloudinary.com') || url.includes('cloudinary.com');
};

// Generate video thumbnail
export const getVideoThumbnail = (publicId: string, options: TransformationOptions = {}): string => {
  if (!publicId || !cloudinaryConfig.cloudName) {
    return '';
  }

  try {
    const video = cld.video(publicId);
    
    // Convert to image thumbnail
    video.format('jpg');
    
    if (options.width || options.height) {
      video.resize(fill().width(options.width || 400).height(options.height || 300));
    }
    
    video.delivery(quality(auto()));
    
    return video.toURL();
  } catch (error) {
    console.error('Error generating video thumbnail:', error);
    return '';
  }
};
