'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import SimpleImageUpload from '@/components/upload/SimpleImageUpload';
import CloudinaryImage from '@/components/CloudinaryImage';
import {
  getRestaurantCardImage,
  getMenuItemImage,
  getUserAvatar,
  getOptimizedImageUrl
} from '@/lib/cloudinary/transform';

export default function CloudinaryTestPage() {
  const { user } = useAuth();
  const [uploadedImages, setUploadedImages] = useState<{
    restaurant?: { url: string; publicId: string };
    menuItem?: { url: string; publicId: string };
    avatar?: { url: string; publicId: string };
  }>({});

  const handleImageUpload = (type: 'restaurant' | 'menuItem' | 'avatar') => 
    (url: string, publicId?: string) => {
      if (publicId) {
        setUploadedImages(prev => ({
          ...prev,
          [type]: { url, publicId }
        }));
      }
    };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🧪 Cloudinary Integration Test
          </h1>
          <p className="text-gray-600">
            Test image uploads and transformations for Tap2Go
          </p>
          {user && (
            <p className="text-sm text-green-600 mt-2">
              ✅ Authenticated as: {user.email}
            </p>
          )}
        </div>

        {!user && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <p className="text-blue-800">
              💡 <strong>Testing Mode:</strong> You can test uploads without signing in. For production use, authentication will be required.
            </p>
          </div>
        )}

        {/* Upload Tests */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Restaurant Image Upload */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🏪 Restaurant Image
            </h2>
            <SimpleImageUpload
              value={uploadedImages.restaurant?.url}
              onChange={handleImageUpload('restaurant')}
              uploadType="restaurant"
              additionalData={{ restaurantId: 'test_restaurant_123' }}
              placeholder="Upload restaurant image"
              disabled={false}
            />
            {uploadedImages.restaurant && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">✅ Uploaded successfully!</p>
                <p className="text-xs text-gray-500 break-all">
                  Public ID: {uploadedImages.restaurant.publicId}
                </p>
              </div>
            )}
          </div>

          {/* Menu Item Upload */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🍔 Menu Item Image
            </h2>
            <SimpleImageUpload
              value={uploadedImages.menuItem?.url}
              onChange={handleImageUpload('menuItem')}
              uploadType="menu-item"
              additionalData={{
                restaurantId: 'test_restaurant_123',
                menuItemId: 'test_menu_item_456'
              }}
              placeholder="Upload menu item image"
              disabled={false}
            />
            {uploadedImages.menuItem && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">✅ Uploaded successfully!</p>
                <p className="text-xs text-gray-500 break-all">
                  Public ID: {uploadedImages.menuItem.publicId}
                </p>
              </div>
            )}
          </div>

          {/* Avatar Upload */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              👤 User Avatar
            </h2>
            <SimpleImageUpload
              value={uploadedImages.avatar?.url}
              onChange={handleImageUpload('avatar')}
              uploadType="avatar"
              additionalData={{ userId: user?.uid || 'test_user_789' }}
              placeholder="Upload user avatar"
              disabled={false}
            />
            {uploadedImages.avatar && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">✅ Uploaded successfully!</p>
                <p className="text-xs text-gray-500 break-all">
                  Public ID: {uploadedImages.avatar.publicId}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Transformation Demo */}
        {Object.keys(uploadedImages).length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              🎨 Image Transformations Demo
            </h2>
            
            {uploadedImages.restaurant && (
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  Restaurant Image Transformations
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Original</p>
                    <CloudinaryImage
                      src={uploadedImages.restaurant.url}
                      alt="Original"
                      width={200}
                      height={150}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Card Size (400x300)</p>
                    <CloudinaryImage
                      src={getRestaurantCardImage(uploadedImages.restaurant.publicId)}
                      alt="Card size"
                      width={200}
                      height={150}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Square Crop</p>
                    <CloudinaryImage
                      src={getOptimizedImageUrl(uploadedImages.restaurant.publicId, {
                        width: 200,
                        height: 200,
                        crop: 'fill'
                      })}
                      alt="Square"
                      width={200}
                      height={200}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Thumbnail</p>
                    <CloudinaryImage
                      src={getOptimizedImageUrl(uploadedImages.restaurant.publicId, {
                        width: 100,
                        height: 100,
                        crop: 'thumb'
                      })}
                      alt="Thumbnail"
                      width={100}
                      height={100}
                      className="rounded-lg shadow-sm mx-auto"
                    />
                  </div>
                </div>
              </div>
            )}

            {uploadedImages.menuItem && (
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  Menu Item Transformations
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Original</p>
                    <CloudinaryImage
                      src={uploadedImages.menuItem.url}
                      alt="Original"
                      width={150}
                      height={150}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Menu Size (300x300)</p>
                    <CloudinaryImage
                      src={getMenuItemImage(uploadedImages.menuItem.publicId)}
                      alt="Menu size"
                      width={150}
                      height={150}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">WebP Format</p>
                    <CloudinaryImage
                      src={getOptimizedImageUrl(uploadedImages.menuItem.publicId, {
                        width: 150,
                        height: 150,
                        format: 'webp',
                        quality: 80
                      })}
                      alt="WebP"
                      width={150}
                      height={150}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Low Quality</p>
                    <CloudinaryImage
                      src={getOptimizedImageUrl(uploadedImages.menuItem.publicId, {
                        width: 150,
                        height: 150,
                        quality: 30
                      })}
                      alt="Low quality"
                      width={150}
                      height={150}
                      className="rounded-lg shadow-sm"
                    />
                  </div>
                </div>
              </div>
            )}

            {uploadedImages.avatar && (
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  Avatar Transformations
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Original</p>
                    <CloudinaryImage
                      src={uploadedImages.avatar.url}
                      alt="Original"
                      width={100}
                      height={100}
                      className="rounded-full shadow-sm mx-auto"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Avatar (200x200)</p>
                    <CloudinaryImage
                      src={getUserAvatar(uploadedImages.avatar.publicId)}
                      alt="Avatar"
                      width={100}
                      height={100}
                      className="rounded-full shadow-sm mx-auto"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Face Detection</p>
                    <CloudinaryImage
                      src={getOptimizedImageUrl(uploadedImages.avatar.publicId, {
                        width: 100,
                        height: 100,
                        crop: 'fill',
                        gravity: 'face'
                      })}
                      alt="Face detection"
                      width={100}
                      height={100}
                      className="rounded-full shadow-sm mx-auto"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-2">Small (50x50)</p>
                    <CloudinaryImage
                      src={getOptimizedImageUrl(uploadedImages.avatar.publicId, {
                        width: 50,
                        height: 50,
                        crop: 'fill'
                      })}
                      alt="Small"
                      width={50}
                      height={50}
                      className="rounded-full shadow-sm mx-auto"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Test Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            📊 Test Results
          </h2>
          <div className="space-y-3">
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span>Environment variables configured</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-500 mr-2">✅</span>
              <span>Cloudinary SDK installed and configured</span>
            </div>
            <div className="flex items-center">
              <span className={uploadedImages.restaurant ? "text-green-500" : "text-gray-400"}>
                {uploadedImages.restaurant ? "✅" : "⏳"}
              </span>
              <span className="ml-2">Restaurant image upload test</span>
            </div>
            <div className="flex items-center">
              <span className={uploadedImages.menuItem ? "text-green-500" : "text-gray-400"}>
                {uploadedImages.menuItem ? "✅" : "⏳"}
              </span>
              <span className="ml-2">Menu item image upload test</span>
            </div>
            <div className="flex items-center">
              <span className={uploadedImages.avatar ? "text-green-500" : "text-gray-400"}>
                {uploadedImages.avatar ? "✅" : "⏳"}
              </span>
              <span className="ml-2">Avatar upload test</span>
            </div>
            <div className="flex items-center">
              <span className={Object.keys(uploadedImages).length > 0 ? "text-green-500" : "text-gray-400"}>
                {Object.keys(uploadedImages).length > 0 ? "✅" : "⏳"}
              </span>
              <span className="ml-2">Image transformations test</span>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            🚀 Next Steps After Testing
          </h3>
          <div className="text-blue-800 space-y-2">
            <p>1. <strong>Upload test images</strong> using the forms above</p>
            <p>2. <strong>Check transformations</strong> - images should automatically optimize</p>
            <p>3. <strong>Verify in Cloudinary Dashboard</strong> - check your media library</p>
            <p>4. <strong>Test different file types</strong> - JPG, PNG, WebP</p>
            <p>5. <strong>Check file size limits</strong> - try uploading large files</p>
            <p>6. <strong>Integration ready!</strong> - Use in your restaurant/menu forms</p>
          </div>
        </div>
      </div>
    </div>
  );
}
