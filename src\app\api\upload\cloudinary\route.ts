import { NextRequest, NextResponse } from 'next/server';
import {
  uploadRestaurantImage,
  uploadMenuItemImage,
  uploadUserAvatar,
  uploadDocument,
  uploadVideo,
  validateFile,
} from '@/lib/cloudinary';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const uploadType = formData.get('uploadType') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!uploadType) {
      return NextResponse.json(
        { error: 'Upload type is required' },
        { status: 400 }
      );
    }

    let result;

    switch (uploadType) {
      case 'restaurant': {
        const restaurantId = formData.get('restaurantId') as string;
        if (!restaurantId) {
          return NextResponse.json(
            { error: 'Restaurant ID is required' },
            { status: 400 }
          );
        }
        result = await uploadRestaurantImage(file, restaurantId);
        break;
      }

      case 'menu-item': {
        const restaurantId = formData.get('restaurantId') as string;
        const menuItemId = formData.get('menuItemId') as string;
        
        if (!restaurantId) {
          return NextResponse.json(
            { error: 'Restaurant ID is required' },
            { status: 400 }
          );
        }
        
        result = await uploadMenuItemImage(file, restaurantId, menuItemId);
        break;
      }

      case 'avatar': {
        const userId = formData.get('userId') as string;
        if (!userId) {
          return NextResponse.json(
            { error: 'User ID is required' },
            { status: 400 }
          );
        }
        result = await uploadUserAvatar(file, userId);
        break;
      }

      case 'document': {
        const userId = formData.get('userId') as string;
        const documentType = formData.get('documentType') as string;
        
        if (!userId || !documentType) {
          return NextResponse.json(
            { error: 'User ID and document type are required' },
            { status: 400 }
          );
        }
        
        result = await uploadDocument(file, userId, documentType);
        break;
      }

      case 'video': {
        const restaurantId = formData.get('restaurantId') as string;
        const videoType = formData.get('videoType') as string || 'promotional';
        
        if (!restaurantId) {
          return NextResponse.json(
            { error: 'Restaurant ID is required' },
            { status: 400 }
          );
        }
        
        result = await uploadVideo(file, restaurantId, videoType);
        break;
      }

      default:
        return NextResponse.json(
          { error: 'Invalid upload type' },
          { status: 400 }
        );
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Upload failed';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
